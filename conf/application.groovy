// Added by the Spring Security Core plugin:
grails.plugin.springsecurity.userLookup.userDomainClassName = 'com.wonderslate.usermanagement.User'
grails.plugin.springsecurity.userLookup.authorityJoinClassName = 'com.wonderslate.usermanagement.UserRole'
grails.plugin.springsecurity.authority.className = 'com.wonderslate.usermanagement.Role'
grails.plugin.springsecurity.controllerAnnotations.staticRules = [
		[pattern: '/',               access: ['permitAll']],
		[pattern: '/error',          access: ['permitAll']],
		[pattern: '/books/index',          access: ['permitAll']],
		[pattern: '/books/index.gsp',      access: ['permitAll']],
		[pattern: '/shutdown',       access: ['permitAll']],
		[pattern: '/assets/**',      access: ['permitAll']],
		[pattern: '/**/js/**',       access: ['permitAll']],
		[pattern: '/**/css/**',      access: ['permitAll']],
		[pattern: '/**/images/**',   access: ['permitAll']],
		[pattern: '/**/favicon.ico', access: ['permitAll']],
		[pattern: '/*',   access: ['permitAll']],
		[pattern: '/**', access: ['permitAll']],

]

grails.plugin.springsecurity.filterChain.chainMap = [
		[pattern: '/assets/**',      filters: 'none'],
		[pattern: '/**/js/**',       filters: 'none'],
		[pattern: '/**/css/**',      filters: 'none'],
		[pattern: '/**/images/**',   filters: 'none'],
		[pattern: '/**/favicon.ico', filters: 'none'],
		[pattern: '/**',             filters: 'JOINED_FILTERS'],
		[pattern: '/api/**',             filters: 'JOINED_FILTERS,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter']
]

grails.plugin.springsecurity.rest.login.active=true
grails.plugin.springsecurity.rest.login.endpointUrl='/api/login'
grails.plugin.springsecurity.rest.login.failureStatusCode=401
grails.plugin.springsecurity.rest.login.useJsonCredentials=true
grails.plugin.springsecurity.rest.login.usernamePropertyName='username'
grails.plugin.springsecurity.rest.login.passwordPropertyName='password'
grails.plugin.springsecurity.rest.token.storage.useGorm = true
grails.plugin.springsecurity.rest.token.storage.gorm.tokenDomainClassName = 'com.wonderslate.usermanagement.AuthenticationToken'
grails.plugin.springsecurity.rest.token.storage.gorm.tokenValuePropertyName = 'token'
grails.plugin.springsecurity.rest.token.storage.gorm.usernamePropertyName = 'username'
grails.plugin.springsecurity.rest.token.validation.active=true
grails.plugin.springsecurity.rest.token.validation.headerName='X-Auth-Token'
grails.plugin.springsecurity.rest.token.validation.endpointUrl='/api/validate'
grails.plugin.springsecurity.rest.token.validation.useBearerToken=false

//cors config.
cors.enabled=true
cors.url.pattern = ['/api/*','/wonderslate/funlearn/*','/**/funlearn/**']
cors.headers=[
		'Access-Control-Allow-Origin': '*',
		'Access-Control-Allow-Credentials': true,
		'Access-Control-Allow-Headers': 'origin, authorization, accept, content-type, x-requested-with,x-auth-token',
		'Access-Control-Allow-Methods': 'GET, HEAD, POST, PUT, DELETE, TRACE, OPTIONS',
		'Access-Control-Max-Age': 3600
]

grails.plugin.springsecurity.rememberMe.alwaysRemember=true

grails.plugin.springsecurity.useSecurityEventListener = true

grails.plugin.springsecurity.onInteractiveAuthenticationSuccessEvent = { e, appCtx ->
	def userLogService = appCtx.getBean('userLogService')
	userLogService.addUserLog("login")
}

grails.plugin.springsecurity.onAuthenticationSuccessEvent
grails.plugin.springsecurity.auth.loginFormUrl = '/security/loginform'
grails.plugin.springsecurity.failureHandler.defaultFailureUrl = '/security/loginfailedmanager'
grails.plugin.springsecurity.successHandler.defaultTargetUrl = '/security/loginmanager'
grails.plugin.springsecurity.logout.afterLogoutUrl='/security/logout'

grails.razorpay.keyId='rzp_test_Hs6ZcOvyPgPdJK'
grails.razorpay.secretKey='0krPs8xnZhudWnlU6Jiymd9h'
grails.processpdf.url='https://process.wonderslate.com'
///Users/<USER>/Documents/technical/wonderslate329/upload/Books_Report_Data_Template.xlsx
grails.basedir.path="/Users/<USER>/Documents/wonderslate329/"
//grails.basedir.path="D:/WonderSlate329/"
grails.appServer.main="true"
//grails.appServer.default="eutkarsh"
//grails.appServer.siteName="e-Utkarsh"
grails.appServer.default="books"
grails.appServer.siteName="Wonderslate"
grails.appServer.serverBaseUrl="https://content.e-utkarsh.com"
grails.appServer.maximumSessions=2

grails.po.invoice.bcc=""

// Added by the Spring Security OAuth2 Google Plugin:
grails.plugin.springsecurity.oauth2.domainClass = 'com.wonderslate.usermanagement.OAuthID'

grails.plugin.springsecurity.oauth2.providers.google.api_key = '920912071511-fpuo9urjbb1kf9dpkfvtdinmdqaifd5c.apps.googleusercontent.com'
grails.plugin.springsecurity.oauth2.providers.google.api_secret = 'ADklVgRkVMZm_Lo8kbbU4uCf'
grails.plugin.springsecurity.oauth2.providers.google.callback =  "/oauth2/google/callback"
grails.plugin.springsecurity.oauth2.providers.google.failureUri =  "/oauth2/google/failure"
grails.plugin.springsecurity.oauth2.providers.google.successUri =  "/oauth2/google/success"

grails.plugin.springsecurity.oauth2.providers.facebook.api_key = '448101868718363'
grails.plugin.springsecurity.oauth2.providers.facebook.api_secret = '********************************'
grails.plugin.springsecurity.oauth2.providers.facebook.callback =  "/oauth2/facebook/callback"
grails.plugin.springsecurity.oauth2.providers.facebook.failureUri =  "/oauth2/facebook/failure"
grails.plugin.springsecurity.oauth2.providers.facebook.successUri =  "/oauth2/facebook/success"

//grails.plugin.springsecurity.filterChain.filterNames = [ 'securityContextPersistenceFilter', 'logoutFilter', 'concurrentSessionFilter', 'rememberMeAuthenticationFilter', 'anonymousAuthenticationFilter', 'exceptionTranslationFilter', 'filterInvocationInterceptor' ]
grails.plugin.springsecurity.logout.handlerNames = ['customSessionLogoutHandler','securityContextLogoutHandler','rememberMeServices']
grails.appServer.driftCode="et3w6ne98zbb"

grails {
	mongodb {
		url = "mongodb://localhost/wsdiscussion"
	}
}